package com.taobao.wireless.orange.service;


import com.taobao.unifiedsession.core.json.JSON;
import com.taobao.wireless.orange.common.model.Pagination;
import com.taobao.wireless.orange.common.model.PaginationResult;
import com.taobao.wireless.orange.common.model.Result;
import com.taobao.wireless.orange.common.util.BeanUtil;
import com.taobao.wireless.orange.common.util.Pipe;
import com.taobao.wireless.orange.manager.ConditionManager;
import com.taobao.wireless.orange.manager.model.ConditionBO;
import com.taobao.wireless.orange.manager.model.ConditionVersionBO;
import com.taobao.wireless.orange.service.model.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

@Service
public class ConditionService {

    @Autowired
    private ConditionManager conditionManager;

    public PaginationResult<ConditionDetailDTO> query(ConditionQueryDTO query, Pagination pagination) {
        return Pipe.of(query)
                .map(q -> BeanUtil.createFromProperties(query, ConditionBO.class))
                .map(q -> conditionManager.query(q, pagination))
                .map(r -> {
                    PaginationResult<ConditionDetailDTO> result = BeanUtil.createFromProperties(r, PaginationResult.class);
                    var conditions = r.getRecords().stream().map(c -> {
                        ConditionDetailDTO condition = BeanUtil.createFromProperties(c, ConditionDetailDTO.class);
                        condition.setName(c.getCondition().getName());
                        condition.setColor(c.getCondition().getColor());
                        condition.setExpression(JSON.parse(c.getExpression(), ConditionExpressionDTO.class));
                        condition.setParameterConditions(BeanUtil.createFromProperties(c.getParameterConditionVersions(), ParameterConditionDTO.class));
                        return condition;
                    }).collect(Collectors.toList());
                    result.setData(conditions);
                    return result;
                })
                .get();
    }

    public Result<List<ConditionDTO>> getAll(ConditionQueryDTO query) {
        return Pipe.of(query)
                .map(q -> BeanUtil.createFromProperties(q, ConditionBO.class))
                .map(q -> conditionManager.getAll(q))
                .map(this::convert)
                .map(Result::new)
                .get();
    }

    private List<ConditionDTO> convert(List<ConditionVersionBO> conditionVersions) {
        return conditionVersions.stream().map(v -> {
            ConditionDTO c = BeanUtil.createFromProperties(v, ConditionDTO.class);
            c.setName(v.getCondition().getName());
            c.setColor(v.getCondition().getColor());
            c.setExpression(JSON.parse(v.getExpression(), ConditionExpressionDTO.class));
            return c;
        }).collect(Collectors.toList());
    }

    public Result<ConditionDetailDTO> getByConditionId(String conditionId) {
        return Pipe.of(conditionId)
                .map(id -> conditionManager.getConditionDetailByConditionId(id))
                .map(c -> {
                    ConditionDetailDTO condition = BeanUtil.createFromProperties(c.getCondition(), ConditionDetailDTO.class);
                    condition.setExpression(JSON.parse(c.getExpression(), ConditionExpressionDTO.class));
                    return condition;
                })
                .map(Result::new)
                .get();
    }

    Long create(ConditionBO condition) {
        return null;
    }

    Boolean update(ConditionBO condition) {
        return null;
    }

    Long countDevice(Long conditionId) {
        return null;
    }
}
