package com.taobao.wireless.orange.service;

import com.taobao.wireless.orange.BaseTest;
import com.taobao.wireless.orange.common.model.Pagination;
import com.taobao.wireless.orange.service.model.ConditionQueryDTO;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

public class ConditionServiceTest extends BaseTest {

    @Autowired
    private ConditionService conditionService;

    @Before
    public void setUp() {
    }

    @Test
    public void query() {
        var query = new ConditionQueryDTO();
        query.setNamespaceId("cd3c20aa956e49bfb1909d0c80f3bda3");
        query.setName("co");
        var result = conditionService.query(query, new Pagination(1, 10));
        Assert.assertTrue(result.isSuccess());
        Assert.assertTrue(result.getData().get(0).getName().contains("co"));
    }

    @Test
    public void getAll() {
    }

    @Test
    public void create() {
    }

    @Test
    public void update() {
    }

    @Test
    public void countDevice() {
    }
}
